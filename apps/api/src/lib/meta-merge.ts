import { pick } from "@std/collections";
import type { YtDlpYouTubeMetadata } from "../services/apify";
import type { YouTubeApiMetadata } from "../services/youtube-api";
import { reduceAspectRatio } from "./math";

type MetadataResponseBase = {
  id: string;
  title: string;
  description: string;
  tags: string[];
  duration: number;
  published_at: string;
  uploader_name: string;
  thumbnails: YouTubeApiMetadata["thumbnails"];
  language: string;
  view_count: number | null;
  like_count: number | null;
  comment_count: number | null;
};

export type MetadataResponse =
  | ({
      type: "partial";
      aspect_ratio: string | null;
    } & MetadataResponseBase)
  | ({
      type: "full";
      aspect_ratio: string;
      chapters: YtDlpYouTubeMetadata["chapters"];
      uploader_id: string;
    } & MetadataResponseBase);

/**
 * Infer aspect ratio from YouTube API thumbnails, preferring 'default' size
 */
function inferAspectRatioFromThumbnails(
  thumbnails: YouTubeApiMetadata["thumbnails"],
): string | null {
  // Prefer 'default' thumbnail, then try others in order of preference
  const preferredOrder: (keyof typeof thumbnails)[] = [
    "default",
    "medium",
    "high",
    "standard",
    "maxres",
  ];

  for (const key of preferredOrder) {
    const thumbnail = thumbnails[key];
    if (thumbnail?.size?.width && thumbnail?.size?.height) {
      return reduceAspectRatio(thumbnail.size.width, thumbnail.size.height);
    }
  }

  return null;
}

export function mergeMetadataResponse({
  ytDlp,
  youtubeApi,
  id,
}: {
  id: string;
  ytDlp?: YtDlpYouTubeMetadata;
  youtubeApi: YouTubeApiMetadata;
}): MetadataResponse {
  const basic = pick(youtubeApi, [
    "title",
    "description",
    "tags",
    "duration",
    "published_at",
    "uploader_name",
    "thumbnails",
    "view_count",
    "like_count",
    "comment_count",
    "language",
  ]);
  if (!ytDlp)
    return {
      type: "partial",
      id,
      aspect_ratio: inferAspectRatioFromThumbnails(youtubeApi.thumbnails),
      ...basic,
    };
  return {
    type: "full",
    id,
    aspect_ratio: ytDlp.aspect_ratio,
    chapters: ytDlp.chapters,
    uploader_id: ytDlp.uploader_id,
    ...basic,
  };
}
