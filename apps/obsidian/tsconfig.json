{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": "src",
    "noEmit": true,
    "incremental": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "esModuleInterop": false,
    "declaration": false,
    "declarationMap": false,
    "lib": [
      "es2022",
      "ESNext.Disposable",
      "DOM",
      "DOM.Iterable",
      "ES2024.Collection",
      "ESNext.Collection",
      "ES2024.Promise",
      
    ],
    "paths": {
      "@/*": ["./*"]
    },
    "moduleDetection": "force",
    "target": "es2022",
    "jsx": "react-jsx",
  },
  "include": [
    "src"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ],
}