import { moment } from "obsidian";
import type { YouTubeMetadata } from "../types/youtube";

/**
 * Formats ISO 8601 date string to user-friendly format
 * @param isoDate - ISO 8601 date string
 * @returns Formatted date string (YYYY-MM-DD)
 */
export function formatPublishedDate(isoDate: string): string {
  try {
    return moment(isoDate).format("YYYY-MM-DD");
  } catch (error) {
    console.warn("Failed to format published date:", isoDate, error);
    return isoDate;
  }
}

/**
 * Cleans and standardizes YouTube tags
 * @param tags - Raw tags array
 * @returns Cleaned and deduplicated tags
 */
export function cleanTags(tags: string[]): string[] {
  if (!Array.isArray(tags)) return [];

  return tags
    .filter((tag) => typeof tag === "string" && tag.trim().length > 0)
    .map((tag) => tag.trim())
    .filter((tag) => tag.length <= 100) // Reasonable tag length limit
    .filter((tag, index, array) => array.indexOf(tag) === index) // Remove duplicates
    .slice(0, 20); // Limit to 20 tags to avoid frontmatter bloat
}

/**
 * Truncates and cleans description text
 * @param description - Raw description text
 * @param maxLength - Maximum length (default: 500)
 * @returns Cleaned and truncated description
 */
export function cleanDescription(description: string, maxLength = 500): string {
  if (typeof description !== "string") return "";

  // Remove excessive whitespace and normalize line breaks
  const cleaned = description
    .replace(/\r\n/g, "\n")
    .replace(/\r/g, "\n")
    .replace(/\n{3,}/g, "\n\n")
    .trim();

  if (cleaned.length <= maxLength) return cleaned;

  // Truncate at word boundary
  const truncated = cleaned.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(" ");

  if (lastSpace > maxLength * 0.8) {
    return `${truncated.substring(0, lastSpace)}...`;
  }

  return `${truncated}...`;
}

/**
 * Selects the best thumbnail URL based on available options
 * @param thumbnails - Available thumbnails
 * @returns Best thumbnail URL or null if none available
 */
export function selectBestThumbnail(
  thumbnails: YouTubeMetadata["thumbnails"],
): string | null {
  if (!thumbnails || typeof thumbnails !== "object") return null;

  // Priority order: maxres > high > standard > medium > default
  const priorities: (keyof typeof thumbnails)[] = [
    "maxres",
    "high",
    "standard",
    "medium",
    "default",
  ];

  for (const quality of priorities) {
    const thumbnail = thumbnails[quality];
    if (thumbnail?.url) {
      return thumbnail.url;
    }
  }

  return null;
}

/**
 * Formats view count to human-readable format
 * @param viewCount - Raw view count
 * @returns Formatted view count (e.g., "1.2M", "45K", "123")
 */
export function formatViewCount(viewCount: number | null): string {
  if (viewCount === null || viewCount < 0) return "N/A";

  if (viewCount >= 1000000) {
    return `${(viewCount / 1000000).toFixed(1)}M`;
  }
  if (viewCount >= 1000) {
    return `${(viewCount / 1000).toFixed(1)}K`;
  }
  return viewCount.toString();
}

/**
 * Formats like count to human-readable format
 * @param likeCount - Raw like count
 * @returns Formatted like count (e.g., "12K", "456")
 */
export function formatLikeCount(likeCount: number | null): string {
  if (likeCount === null || likeCount < 0) return "N/A";

  if (likeCount >= 1000) {
    return `${(likeCount / 1000).toFixed(1)}K`;
  }
  return likeCount.toString();
}

/**
 * Processes raw YouTube metadata for Obsidian frontmatter
 * @param metadata - Raw YouTube metadata
 * @returns Processed metadata ready for frontmatter
 */
export function processMetadataForFrontmatter(metadata: YouTubeMetadata) {
  return {
    title: metadata.title || metadata.id,
    description: cleanDescription(metadata.description),
    duration: metadata.duration,
    date: formatPublishedDate(metadata.published_at),
    author: metadata.uploader_name,
    "view-count": formatViewCount(metadata.view_count),
    "like-count": formatLikeCount(metadata.like_count),
    "aspect-ratio": metadata.aspect_ratio,
    cover: selectBestThumbnail(metadata.thumbnails),
    language: metadata.language,
    tags: cleanTags(metadata.tags),
  };
}
