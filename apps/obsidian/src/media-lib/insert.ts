import type { <PERSON>ada<PERSON><PERSON><PERSON>, Vault, T<PERSON><PERSON>, FileManager } from "obsidian";
import type { MediaInfo } from "@/def/media-info";
import { isAudioFile, type MediaType } from "@/def/media-type";

import { assertNever } from "@std/assert/unstable-never";
import type MxSettings from "@/settings/registry";
import type { MediaLibraryIndex } from "./indexer";
import { createMediaId, MEDIA_ID_FIELD } from "@/def/media-id";
import { defaultMediaMeta, type MediaNoteMeta } from "./parse";
import { YouTubeApiService } from "@/api/youtube-service";
import { processMetadataForFrontmatter } from "@/api/utils/metadata-processor";

export interface MediaLibraryItemInit {
  title: string;
  fm: (newNotePath: string) => Record<string, any>;
  sourcePath?: string;
}

export interface MediaLibItemNoteContext {
  fileManager: FileManager;
  mediaLibIndex: MediaLibraryIndex;
  metadataCache: MetadataCache;
  vault: Vault;
  settings: MxSettings;
}

/**
 * get or create (if not exist) media lib item note
 */
export async function mediaLibItemNoteFor(
  mediaInfo: MediaInfo,
  ctx: MediaLibItemNoteContext,
): Promise<{ note: TFile; uid: string; meta: MediaNoteMeta }> {
  const media = ctx.mediaLibIndex.getMediaMeta(mediaInfo);
  if (media) {
    return {
      note: media.note,
      uid: media.uid,
      get meta() {
        return media.meta;
      },
    };
  }

  const uid = createMediaId();
  const noteName = filenameFor(mediaInfo, uid[MEDIA_ID_FIELD]);
  const meta = { ...defaultMediaMeta };

  if (mediaInfo.type === "file") {
    const mediaType: MediaType = isAudioFile(mediaInfo.file)
      ? "audio"
      : "video";
    const file = mediaInfo.file;

    const note = await createMediaLibItem(
      {
        noteName,
        fm: (newNotePath) => ({
          ...uid,
          [mediaType]: `[[${ctx.metadataCache.fileToLinktext(file, newNotePath)}]]`,
        }),
      },
      ctx,
    );
    return {
      note,
      uid: uid[MEDIA_ID_FIELD],
      meta,
    };
  }
  if (mediaInfo.type === "url:direct" || mediaInfo.type === "url:hosted") {
    let extraFm: Record<string, any> = {};
    if (mediaInfo.type === "url:hosted" && mediaInfo.vid.host === "youtube") {
      const youtubeService = new YouTubeApiService();
      const metadata = await youtubeService.getVideoMetadata(mediaInfo.vid.vid);
      extraFm = { ...extraFm, ...processMetadataForFrontmatter(metadata) };
    }

    const note = await createMediaLibItem(
      {
        noteName,
        fm: () => ({ ...uid, media: mediaInfo.url.toString(), ...extraFm }),
      },
      ctx,
    );
    return {
      note,
      uid: uid[MEDIA_ID_FIELD],
      meta,
    };
  }
  assertNever(mediaInfo);
}

async function createMediaLibItem(
  init: {
    noteName: string;
    fm: (notePath: string) => Record<string, any>;
  },
  ctx: Pick<MediaLibItemNoteContext, "fileManager" | "vault" | "settings">,
) {
  const { noteName: filename, fm } = init;
  const { fileManager, vault, settings } = ctx;

  const folderPath = (await settings.loaded)["media-lib.folder-path"];
  let folder = vault.getFolderByPath(folderPath);
  if (!folder) {
    folder = await vault.createFolder(folderPath);
  }

  const newNote = await fileManager.createNewFile(
    folder,
    filename,
    "md",
    "---\n---\n",
  );
  await fileManager.processFrontMatter(newNote, (fn) => {
    Object.assign(fn, fm(newNote.path));
  });
  return newNote;
}

function filenameFor(mediaInfo: MediaInfo, uid: string) {
  if (mediaInfo.type === "file") {
    return `file-${uid.slice(0, 8)}`;
    // return `file-${djb2a(mediaInfo.file.path).toString(36)}`;
  }
  if (mediaInfo.type === "url:direct" || mediaInfo.type === "url:hosted") {
    return `url-${uid.slice(0, 8)}`;
    // return `url-${djb2a(mediaInfo.url.toString()).toString(36)}`;
  }
  assertNever(mediaInfo);
}
